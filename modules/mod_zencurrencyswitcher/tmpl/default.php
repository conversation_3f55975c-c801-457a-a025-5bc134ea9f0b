<div class="currency currency-switcher-enhanced">
	<span class="flag" style="background: url(/administrator/components/com_zenadmin/assets/images/flags-iso/16/<?= strtolower(substr(ZenSessionHelper::getUsersCurrency()->code, 0, 2)) ?>.png) no-repeat left center;"></span>
	<form method="post" class="currency-switcher-form">
		<label for="ftr-select-currency" class="visually-hidden">Select Currency</label>
		<select name="currency" id="ftr-select-currency" class="zen-dropdown currency-select" onChange="this.form.submit();" aria-label="Currency selector">
			<?php foreach ($currencies as $c) : ?>
        <option value="<?= $c->code ?>" <?= ZenSessionHelper::getUsersCurrency()->code == $c->code ? ' selected="selected"' : ''; ?>>
          <?= $c->symbol ?> <?= $c->code ?>
        </option>
			<?php endforeach ?>
		</select>
		<?php unset($query['currency']); ?>
		<?php foreach($query as $k => $v) : ?>
			<?= '<input type="hidden" name="'.$k.'" value="'.$v.'">' ?>
		<?php endforeach ?>
	</form>
</div>
<style>
.currency-switcher-enhanced {
	display: inline-flex;
	align-items: center;
	gap: 8px;
	padding: 8px 12px;
	background: #f8f9fa;
	border-radius: 6px;
	transition: background-color 0.2s;
}
.currency-switcher-enhanced:hover {
	background: #e9ecef;
}
.currency-select {
	font-weight: 600;
	border: 1px solid #dee2e6;
	border-radius: 4px;
	padding: 4px 8px;
	cursor: pointer;
}
.visually-hidden {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}
</style>
