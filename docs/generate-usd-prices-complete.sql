-- ============================================================================
-- GENERATE USD PRICES FOR ALL HOLIDAYS
-- ============================================================================
-- This script creates USD price records for all existing GBP prices
-- Run this ONCE to bulk-generate all USD prices
-- After this, the ForexPricing plugin will handle new/updated prices
-- Database prefix: ev_
-- ============================================================================

-- Step 1: Check current state (BEFORE)
-- ============================================================================
SELECT '=== BEFORE: Current Price Counts ===' as status;

SELECT
    currency_code,
    COUNT(*) as price_count,
    MIN(value) as min_price,
    MAX(value) as max_price,
    ROUND(AVG(value), 2) as avg_price
FROM ev_zenholidayprices
WHERE state = 1
GROUP BY currency_code;

-- Step 2: Verify USD currency and exchange rate exist
-- ============================================================================
SELECT '=== Checking USD Currency Setup ===' as status;

SELECT
    code,
    symbol,
    title,
    state
FROM ev_zencurrencies
WHERE code = 'USD';

-- If the above returns no rows, USD currency is not set up!
-- You need to add it first in Components → Zen Admin → Currencies

SELECT '=== Checking USD Exchange Rate ===' as status;

SELECT
    currency_code,
    rate,
    valid_from,
    rounding_increment,
    state
FROM ev_zencurrencyrates
WHERE currency_code = 'USD'
ORDER BY valid_from DESC
LIMIT 1;

-- If the above returns no rows, you need to set an exchange rate first!
-- Typical rate: 1.27 (meaning £1 = $1.27)

-- Step 3: Generate USD prices from GBP prices
-- ============================================================================
SELECT '=== Generating USD Prices ===' as status;

-- Get the latest USD exchange rate
SET @usd_rate = (
    SELECT rate
    FROM ev_zencurrencyrates
    WHERE currency_code = 'USD'
        AND state = 1
        AND valid_from <= NOW()
    ORDER BY valid_from DESC
    LIMIT 1
);

-- Display the rate we're using
SELECT CONCAT('Using USD exchange rate: ', IFNULL(@usd_rate, 'NOT FOUND')) as info;

-- Check if rate exists
SELECT
    CASE
        WHEN @usd_rate IS NULL THEN 'ERROR: No USD exchange rate found! Please add one first.'
        WHEN @usd_rate = 0 THEN 'ERROR: USD exchange rate is 0! Please fix the rate.'
        WHEN @usd_rate < 0.5 OR @usd_rate > 5 THEN 'WARNING: USD rate seems unusual. Typical range is 1.20-1.35'
        ELSE 'OK: Exchange rate looks valid'
    END as rate_check;

-- Only proceed if rate exists
-- Create USD price for every GBP price that doesn't already have a USD equivalent
INSERT INTO ev_zenholidayprices (
    date_id,
    type_id,
    currency_code,
    value,
    spaces,
    state,
    created,
    created_by
)
SELECT
    gbp.date_id,
    gbp.type_id,
    'USD' as currency_code,
    ROUND(gbp.value * @usd_rate, 0) as value,  -- Round to whole dollars
    gbp.spaces,
    gbp.state,
    NOW() as created,
    0 as created_by
FROM ev_zenholidayprices gbp
WHERE gbp.currency_code = 'GBP'
  AND gbp.state = 1
  AND NOT EXISTS (
    -- Don't create if USD price already exists for this date/type
    SELECT 1
    FROM ev_zenholidayprices usd
    WHERE usd.date_id = gbp.date_id
      AND usd.type_id = gbp.type_id
      AND usd.currency_code = 'USD'
  );

-- Show how many were created
SELECT ROW_COUNT() as 'USD_Prices_Created';

-- Step 4: Check results (AFTER)
-- ============================================================================
SELECT '=== AFTER: Updated Price Counts ===' as status;

SELECT
    currency_code,
    COUNT(*) as price_count,
    MIN(value) as min_price,
    MAX(value) as max_price,
    ROUND(AVG(value), 2) as avg_price
FROM ev_zenholidayprices
WHERE state = 1
GROUP BY currency_code;

-- Expected result: GBP and USD should have same count
-- Example:
-- currency_code | price_count | min_price | max_price | avg_price
-- GBP           | 1234        | 500       | 5000      | 1800.00
-- USD           | 1234        | 635       | 6350      | 2286.00

-- Step 5: Sample verification - Check a few specific prices
-- ============================================================================
SELECT '=== Sample Price Comparison (First 10 Dates) ===' as status;

SELECT
    d.id as date_id,
    d.start as departure_date,
    h.title as holiday_name,
    gbp.value as gbp_price,
    gbp.currency_code as gbp_currency,
    usd.value as usd_price,
    usd.currency_code as usd_currency,
    ROUND(gbp.value * @usd_rate, 0) as expected_usd,
    CASE
        WHEN usd.value = ROUND(gbp.value * @usd_rate, 0) THEN '✓ Correct'
        ELSE '✗ Mismatch'
    END as verification
FROM ev_zenholidaydates d
JOIN ev_zenholidayversions v ON d.version_id = v.id
JOIN ev_zenholidays h ON v.holiday_id = h.id
LEFT JOIN ev_zenholidayprices gbp ON (
    gbp.date_id = d.id
    AND gbp.type_id = 1
    AND gbp.currency_code = 'GBP'
)
LEFT JOIN ev_zenholidayprices usd ON (
    usd.date_id = d.id
    AND usd.type_id = 1
    AND usd.currency_code = 'USD'
)
WHERE gbp.id IS NOT NULL
ORDER BY d.start ASC
LIMIT 10;

-- Step 6: Check for any issues
-- ============================================================================
SELECT '=== Checking for Issues ===' as status;

-- Check for dates with GBP but no USD
SELECT
    COUNT(*) as dates_missing_usd
FROM ev_zenholidayprices gbp
WHERE gbp.currency_code = 'GBP'
  AND gbp.state = 1
  AND NOT EXISTS (
    SELECT 1
    FROM ev_zenholidayprices usd
    WHERE usd.date_id = gbp.date_id
      AND usd.type_id = gbp.type_id
      AND usd.currency_code = 'USD'
      AND usd.state = 1
  );

-- Should be 0 after running this script

-- Step 7: Final summary
-- ============================================================================
SELECT '=== SCRIPT COMPLETE ===' as status;

SELECT CONCAT(
    'Summary: ',
    (SELECT COUNT(*) FROM ev_zenholidayprices WHERE currency_code = 'USD' AND state = 1),
    ' USD prices created at rate of $',
    @usd_rate,
    ' per £1'
) as summary;

-- ============================================================================
-- NEXT STEPS:
-- ============================================================================
-- 1. Clear Joomla cache: System → Clear Cache → Select All → Delete
-- 2. Visit your website homepage
-- 3. Look at debug panel - verify USD currency is detected
-- 4. Switch to USD using currency switcher
-- 5. Verify trip card prices show in USD with $ symbol
-- 6. Click on a trip - verify hero price shows in USD
-- 7. Check Dates & Prices tab - verify all prices show in USD
--
-- If prices still don't show:
-- - Check browser console for JavaScript errors (F12)
-- - Verify USD symbol is set to "$" not "US$" (run fix-usd-symbol.sql)
-- - Check that from_price->prices array contains USD entries
-- ============================================================================
