# Currency Switching Testing Plan

**Project**: Multi-Currency Support Implementation
**Date Created**: 2025-10-09
**Target Go-Live**: Week of 2025-10-14 (coordinated with Rezkit)

## Overview

This testing plan covers all aspects of the multi-currency implementation, focusing on USD and GBP currency support with automatic detection and manual switching capabilities.

## Pre-Testing Checklist

### Database Verification

- [ ] **Verify USD currency is enabled**
  ```sql
  SELECT id, code, symbol, title, state
  FROM joomla_zencurrencies
  WHERE code IN ('USD', 'GBP');
  ```
  - Both currencies should have `state = 1` (Published)

- [ ] **Verify USD exchange rates exist**
  ```sql
  SELECT * FROM joomla_zencurrencyrates
  WHERE currency_code = 'USD'
  ORDER BY valid_from DESC LIMIT 5;
  ```
  - Current exchange rate should exist with recent `valid_from` date

- [ ] **Verify USD prices exist for test holidays**
  ```sql
  SELECT h.title, d.start_date, p.currency_code, p.value
  FROM joomla_zenholidayprices p
  JOIN joomla_zenholidaydates d ON p.date_id = d.id
  JOIN joomla_zenholidayversions v ON d.version_id = v.id
  JOIN joomla_zenholidays h ON v.holiday_id = h.id
  WHERE p.currency_code = 'USD'
  LIMIT 10;
  ```
  - Should return USD prices for various holidays

## Test Scenarios

### 1. IP-Based Currency Detection

#### Test 1.1: US Visitor Gets USD
**Environment**: Staging
**Prerequisites**: Clear browser cookies and session
**VPN/Proxy**: US IP address

**Steps**:
1. Connect to US VPN
2. Clear all cookies and session data
3. Visit homepage: https://staging.evertrek.co.uk
4. Navigate to any trip page (e.g., Everest Base Camp)

**Expected Results**:
- [ ] Prices display in USD ($)
- [ ] Currency switcher shows USD selected
- [ ] Session variable `__geoip_currency` = 'USD'
- [ ] All "Book Now" URLs contain `&currency=USD`

#### Test 1.2: UK Visitor Gets GBP
**Environment**: Staging
**Prerequisites**: Clear browser cookies and session
**VPN/Proxy**: UK IP address

**Steps**:
1. Connect to UK VPN (or use natural UK IP)
2. Clear all cookies and session data
3. Visit homepage: https://staging.evertrek.co.uk
4. Navigate to any trip page

**Expected Results**:
- [ ] Prices display in GBP (£)
- [ ] Currency switcher shows GBP selected
- [ ] Session variable `__geoip_currency` = 'GBP'
- [ ] All "Book Now" URLs contain `&currency=GBP`

#### Test 1.3: Non-US/UK Visitor Gets GBP (Default)
**Environment**: Staging
**Prerequisites**: Clear browser cookies and session
**VPN/Proxy**: Any other country (e.g., Canada, Australia, Germany)

**Steps**:
1. Connect to non-US/UK VPN
2. Clear all cookies and session data
3. Visit homepage
4. Navigate to any trip page

**Expected Results**:
- [ ] Prices display in GBP (£)
- [ ] Currency switcher shows GBP selected
- [ ] Session variable `__geoip_currency` = 'GBP'
- [ ] All "Book Now" URLs contain `&currency=GBP`

### 2. Manual Currency Switching

#### Test 2.1: Switch from GBP to USD
**Environment**: Staging
**Prerequisites**: Start with GBP (UK visitor or default)

**Steps**:
1. Verify you're seeing GBP prices
2. Locate currency switcher in header/footer
3. Click dropdown and select "$ USD"
4. Wait for page reload
5. Navigate to different trip pages

**Expected Results**:
- [ ] All prices convert to USD
- [ ] Currency symbols change from £ to $
- [ ] Currency switcher shows USD selected
- [ ] Selection persists across page navigation
- [ ] Session variable `__geoip_currency` = 'USD'
- [ ] All "Book Now" URLs contain `&currency=USD`

#### Test 2.2: Switch from USD to GBP
**Environment**: Staging
**Prerequisites**: Start with USD (US visitor)

**Steps**:
1. Verify you're seeing USD prices
2. Locate currency switcher in header/footer
3. Click dropdown and select "£ GBP"
4. Wait for page reload
5. Navigate to different trip pages

**Expected Results**:
- [ ] All prices convert to GBP
- [ ] Currency symbols change from $ to £
- [ ] Currency switcher shows GBP selected
- [ ] Selection persists across page navigation
- [ ] Session variable `__geoip_currency` = 'GBP'
- [ ] All "Book Now" URLs contain `&currency=GBP`

#### Test 2.3: Currency Persistence Across Session
**Environment**: Staging

**Steps**:
1. Switch to USD
2. Close browser tab (but not entire browser)
3. Open new tab and return to site
4. Check currency display

**Expected Results**:
- [ ] Currency remains USD (session persists)
- [ ] No automatic reversion to IP-based detection

### 3. Booking URL Structure

#### Test 3.1: Verify New URL Format - GBP
**Environment**: Staging
**Currency**: GBP

**Steps**:
1. Navigate to any trip with available dates
2. Expand dates & prices section
3. Right-click on "Book Now" button → Copy Link Address
4. Paste and inspect URL structure

**Expected Results**:
- [ ] URL format: `https://secure.evertrek.co.uk/booking/?sku=holiday:{dateId}-1&currency=GBP`
- [ ] `{dateId}` is numeric (not priceId)
- [ ] `-1` suffix present (priceTypeId)
- [ ] `&currency=GBP` parameter present
- [ ] No old `holiday:{priceId}` format

**Example URLs to verify**:
```
✅ CORRECT: ?sku=holiday:12345-1&currency=GBP
❌ WRONG:   ?sku=holiday:67890 (old priceId format)
❌ WRONG:   ?sku=holiday:12345-1 (missing currency parameter)
```

#### Test 3.2: Verify New URL Format - USD
**Environment**: Staging
**Currency**: USD

**Steps**:
1. Switch currency to USD
2. Navigate to same trip page
3. Right-click on "Book Now" button → Copy Link Address
4. Paste and inspect URL structure

**Expected Results**:
- [ ] URL format: `https://secure.evertrek.co.uk/booking/?sku=holiday:{dateId}-1&currency=USD`
- [ ] Same `{dateId}` as GBP version
- [ ] `&currency=USD` parameter present

#### Test 3.3: Multiple Dates - Same Trip
**Environment**: Staging

**Steps**:
1. Find trip with multiple departure dates
2. Copy "Book Now" URLs from 3 different dates
3. Compare dateId values

**Expected Results**:
- [ ] Each date has different `dateId`
- [ ] All have `-1` suffix
- [ ] All have same `&currency=` parameter
- [ ] dateId values are consistent (don't change on page refresh)

### 4. Price Display & Formatting

#### Test 4.1: USD Price Display
**Environment**: Staging
**Currency**: USD

**Steps**:
1. Switch to USD
2. Browse multiple trip pages
3. Check price formatting in:
   - Hero section ("From $X,XXX")
   - Dates & Prices table
   - Payment plan calculations

**Expected Results**:
- [ ] Currency symbol is `$` (not `£`)
- [ ] Numbers formatted with commas (e.g., $2,450)
- [ ] No decimal places for whole numbers
- [ ] Consistent formatting across all price displays
- [ ] Payment plans calculate correctly (deposit + monthly payments)

#### Test 4.2: GBP Price Display
**Environment**: Staging
**Currency**: GBP

**Steps**:
1. Switch to GBP
2. Browse same trip pages
3. Check price formatting in same locations

**Expected Results**:
- [ ] Currency symbol is `£` (not `$`)
- [ ] Numbers formatted with commas (e.g., £1,995)
- [ ] No decimal places for whole numbers
- [ ] Consistent formatting across all price displays
- [ ] Payment plans calculate correctly

#### Test 4.3: Price Consistency
**Environment**: Staging

**Steps**:
1. Note USD price for specific trip/date
2. Switch to GBP
3. Note GBP price for same trip/date
4. Calculate exchange rate ratio
5. Compare with database exchange rate

**Expected Results**:
- [ ] Prices reflect correct exchange rate
- [ ] Ratio matches database `joomla_zencurrencyrates.rate`
- [ ] No rounding errors causing major discrepancies

#### Test 4.4: Payment Plan Calculations
**Environment**: Staging

**Steps**:
1. Select trip with payment plan option
2. In USD, verify monthly payment calculation:
   - Total price minus deposit (£200 or $200 depending on currency)
   - Divided by number of months
   - Rounded appropriately
3. Switch to GBP and verify same calculation

**Expected Results**:
- [ ] USD monthly payment: `(Total - 200) / months`
- [ ] GBP monthly payment: `(Total - 200) / months`
- [ ] No decimal places shown
- [ ] "OR" text displays correctly
- [ ] Format: "$XXX / 6 months" or "£XXX / 6 months"

### 5. ZenProvider API Integration

#### Test 5.1: API Accepts New URL Format - GBP
**Environment**: Staging (requires API credentials)
**Method**: POST to ZenProvider API

**Steps**:
1. Construct API request with new SKU format:
   ```json
   {
     "productId": "holiday:12345-1",
     "currency": "GBP",
     "passengers": [...]
   }
   ```
2. Send authenticated request to `/index.php?option=com_zenprovider&view=book&format=json`
3. Inspect response

**Expected Results**:
- [ ] API returns `201 Created` status
- [ ] Response includes `reference` field
- [ ] No errors about invalid format
- [ ] Booking reservation created in database

#### Test 5.2: API Accepts New URL Format - USD
**Environment**: Staging
**Method**: POST to ZenProvider API

**Steps**:
1. Construct API request with USD:
   ```json
   {
     "productId": "holiday:12345-1",
     "currency": "USD",
     "passengers": [...]
   }
   ```
2. Send authenticated request
3. Inspect response

**Expected Results**:
- [ ] API returns `201 Created` status
- [ ] Correct USD price used for booking
- [ ] Response includes `reference` field

#### Test 5.3: API Handles Missing Currency Parameter
**Environment**: Staging

**Steps**:
1. Construct API request WITHOUT currency parameter:
   ```json
   {
     "productId": "holiday:12345-1",
     "passengers": [...]
   }
   ```
2. Send authenticated request
3. Inspect response

**Expected Results**:
- [ ] API defaults to GBP (fallback)
- [ ] Booking succeeds with `201 Created`
- [ ] No fatal errors

#### Test 5.4: API Rejects Invalid Currency
**Environment**: Staging

**Steps**:
1. Construct API request with invalid currency:
   ```json
   {
     "productId": "holiday:12345-1",
     "currency": "EUR",
     "passengers": [...]
   }
   ```
2. Send authenticated request
3. Inspect response

**Expected Results**:
- [ ] API returns `404 Not Found` (price not found for EUR)
- [ ] Error message: "Invalid Package Specified"
- [ ] No booking created

### 6. End-to-End Booking Flow

#### Test 6.1: Complete GBP Booking
**Environment**: Staging
**Currency**: GBP

**Steps**:
1. Browse trip page in GBP
2. Click "Book Now" for available date
3. Redirect to Rezkit checkout
4. Verify price and currency in checkout
5. (If test environment allows) Complete booking

**Expected Results**:
- [ ] Redirect to `secure.evertrek.co.uk/booking/`
- [ ] URL contains correct `sku=holiday:{dateId}-1&currency=GBP`
- [ ] Rezkit displays GBP pricing
- [ ] No price discrepancies
- [ ] Booking completes successfully

#### Test 6.2: Complete USD Booking
**Environment**: Staging
**Currency**: USD

**Steps**:
1. Switch to USD
2. Browse trip page
3. Click "Book Now" for available date
4. Redirect to Rezkit checkout
5. Verify price and currency in checkout
6. (If test environment allows) Complete booking

**Expected Results**:
- [ ] Redirect to `secure.evertrek.co.uk/booking/`
- [ ] URL contains correct `sku=holiday:{dateId}-1&currency=USD`
- [ ] Rezkit displays USD pricing
- [ ] No price discrepancies
- [ ] Booking completes successfully

#### Test 6.3: Currency Mismatch Handling
**Environment**: Staging

**Steps**:
1. Browse trip in GBP
2. Add `?currency=USD` to URL manually
3. Check if price updates
4. Click "Book Now"
5. Verify currency in checkout

**Expected Results**:
- [ ] Price updates to USD when URL parameter changes
- [ ] "Book Now" URL reflects USD
- [ ] Rezkit receives USD booking
- [ ] No confusion about currency

### 7. Browser & Device Compatibility

#### Test 7.1: Desktop Browsers
**Environments**: Staging

**Browsers to Test**:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest, macOS)
- [ ] Edge (latest)

**For Each Browser**:
1. Test IP-based detection
2. Test manual currency switching
3. Verify price display formatting
4. Test booking URL generation
5. Check currency switcher UI

#### Test 7.2: Mobile Browsers
**Environments**: Staging

**Devices to Test**:
- [ ] iPhone (Safari)
- [ ] Android (Chrome)
- [ ] iPad (Safari)

**For Each Device**:
1. Test currency switcher visibility and usability
2. Verify price formatting on mobile
3. Test "Book Now" button functionality
4. Check responsive design of currency selector

#### Test 7.3: Responsive Design
**Environment**: Staging

**Steps**:
1. Open DevTools
2. Toggle device emulation
3. Test at breakpoints: 320px, 768px, 1024px, 1440px
4. Check currency switcher placement and visibility

**Expected Results**:
- [ ] Currency switcher visible at all breakpoints
- [ ] No UI overlap or cutoff
- [ ] Touch targets adequately sized (mobile)
- [ ] Dropdown works on touch devices

### 8. Session & Cookie Handling

#### Test 8.1: Session Persistence
**Environment**: Staging

**Steps**:
1. Switch to USD
2. Navigate through 5+ different pages
3. Check currency remains USD
4. Open new tab (same browser session)
5. Navigate to site
6. Check currency

**Expected Results**:
- [ ] Currency persists across navigation
- [ ] New tab in same session inherits currency
- [ ] Session variable `__geoip_currency` stable

#### Test 8.2: Cookie Expiry
**Environment**: Staging

**Steps**:
1. Switch to USD
2. Check locale cookie in DevTools (Application → Cookies)
3. Note expiry date (should be ~30 days)
4. Close browser completely
5. Reopen and visit site
6. Check currency

**Expected Results**:
- [ ] Cookie set with 30-day expiry
- [ ] Cookie format: `locale={language}|{currency}`
- [ ] Currency persists after browser restart (if cookie exists)
- [ ] If no cookie, IP detection runs again

### 9. Error Handling & Edge Cases

#### Test 9.1: Invalid Date ID
**Environment**: Staging

**Steps**:
1. Manually construct URL with invalid dateId:
   `?sku=holiday:99999999-1&currency=GBP`
2. Click to proceed to booking

**Expected Results**:
- [ ] ZenProvider API returns `404 Not Found`
- [ ] Error message displayed (not fatal crash)
- [ ] User can navigate back

#### Test 9.2: Trip with No USD Prices
**Environment**: Staging (if any trips lack USD prices)

**Steps**:
1. Switch to USD
2. Navigate to trip with no USD prices
3. Observe behavior

**Expected Results**:
- [ ] Trip displays without prices, OR
- [ ] Trip automatically falls back to GBP with notice, OR
- [ ] Trip shows "Price not available in USD"
- [ ] No PHP errors or crashes

#### Test 9.3: Concurrent Currency Switch
**Environment**: Staging

**Steps**:
1. Open two browser tabs
2. Tab 1: Set to GBP
3. Tab 2: Switch to USD
4. Tab 1: Refresh page
5. Check currency in Tab 1

**Expected Results**:
- [ ] Tab 1 updates to USD (session shared)
- [ ] No conflicts or errors
- [ ] Consistent currency across tabs

### 10. Performance & Load Testing

#### Test 10.1: Currency Detection Speed
**Environment**: Staging

**Steps**:
1. Clear cache and session
2. Measure time from page load to currency detection
3. Use browser DevTools Performance tab

**Expected Results**:
- [ ] Currency detection completes within 500ms
- [ ] No noticeable delay in price display
- [ ] No FOUC (Flash of Unstyled Content)

#### Test 10.2: API Response Time
**Environment**: Staging

**Steps**:
1. Send 10 sequential booking requests to ZenProvider API
2. Measure response time for each
3. Calculate average

**Expected Results**:
- [ ] Average response time < 2 seconds
- [ ] No timeouts
- [ ] Consistent performance across requests

## Regression Testing

### Areas to Verify No Breaking Changes

#### Test R1: GBP-Only Legacy Functionality
**Environment**: Staging

**Steps**:
1. Test existing GBP bookings still work
2. Verify all existing trips display correctly
3. Check payment plans calculate as before
4. Test search and filter functionality

**Expected Results**:
- [ ] All GBP functionality unchanged
- [ ] No regression in existing features
- [ ] Legacy links still work (if any)

#### Test R2: Non-Holiday Booking Items
**Environment**: Staging

**Steps**:
1. Test activity bookings (if applicable)
2. Test accommodation bookings (if applicable)
3. Verify these use correct currency

**Expected Results**:
- [ ] Activities and accommodations respect user's currency
- [ ] No errors in booking process
- [ ] Prices display correctly

#### Test R3: Admin Functions
**Environment**: Staging (admin access required)

**Steps**:
1. Log into Joomla admin
2. Edit a holiday price
3. Verify ForexPricing plugin generates USD price
4. Check currency management interface

**Expected Results**:
- [ ] Editing GBP price triggers USD price update
- [ ] ForexPricing plugin works correctly
- [ ] Both currencies show in admin

## Pre-Go-Live Checklist

- [ ] All critical tests passed (Sections 1-6)
- [ ] All browser compatibility tests passed (Section 7)
- [ ] No critical bugs identified
- [ ] Performance acceptable (Section 10)
- [ ] Regression testing clean (Section 11)
- [ ] Database has USD prices for all live trips
- [ ] Currency exchange rates up to date
- [ ] Rezkit confirms ready for new URL format
- [ ] Staging deployment successful
- [ ] Monitoring tools configured for production
- [ ] Rollback plan documented and ready
- [ ] Go-live window coordinated with Rezkit

## Go-Live Deployment Checklist

### Pre-Deployment (1 hour before)

- [ ] Confirm Rezkit is ready
- [ ] Take database backup
- [ ] Take codebase snapshot/commit
- [ ] Clear Joomla cache
- [ ] Notify team of deployment window

### Deployment Steps

1. [ ] Deploy code changes to production
2. [ ] Clear all caches (Joomla, CDN, browser)
3. [ ] Verify homepage loads
4. [ ] Quick smoke test: GBP trip → USD trip → Book Now URL
5. [ ] Monitor error logs

### Post-Deployment (1 hour after)

- [ ] Test 3 GBP bookings end-to-end
- [ ] Test 3 USD bookings end-to-end
- [ ] Monitor server error logs
- [ ] Monitor Rezkit for any issues
- [ ] Verify analytics tracking currency
- [ ] Check currency switcher usage

### Rollback Criteria

If any of these occur, initiate rollback:
- [ ] Booking success rate drops below 90%
- [ ] Fatal PHP errors on trip pages
- [ ] ZenProvider API returning errors > 10% of requests
- [ ] Rezkit unable to process bookings
- [ ] Price display completely broken

### Rollback Procedure

1. Revert code to previous commit
2. Clear all caches
3. Verify rollback successful
4. Notify team
5. Schedule post-mortem

## Post-Go-Live Monitoring (First Week)

### Daily Checks (Days 1-7)

- [ ] Review error logs for currency-related issues
- [ ] Check booking conversion rate (GBP vs USD)
- [ ] Verify currency switching analytics
- [ ] Monitor customer support tickets
- [ ] Check for any Rezkit integration issues

### Weekly Report Metrics

- **Currency Distribution**: % of visitors choosing USD vs GBP
- **Booking Conversion Rate**: By currency
- **Support Tickets**: Currency-related issues
- **API Errors**: Any ZenProvider issues
- **Performance**: Page load times by currency

## Success Criteria

### Must-Have (Launch Blockers)

- ✅ US visitors see USD prices automatically
- ✅ All other visitors see GBP prices
- ✅ Manual currency switching works
- ✅ Booking URLs follow new format with currency parameter
- ✅ ZenProvider API processes multi-currency bookings
- ✅ No fatal errors or crashes

### Should-Have (Post-Launch Fixes OK)

- ✅ Currency switcher UI polished and visible
- ✅ Price formatting perfect in all locations
- ✅ Session persistence across all scenarios
- ✅ Mobile experience optimized

### Nice-to-Have (Future Enhancements)

- Currency preference remembered across visits (cookie)
- Additional currencies (EUR, AUD, CAD)
- Currency symbol localization
- Dynamic exchange rate updates

## Testing Sign-Off

| Test Category | Status | Tester | Date | Notes |
|---------------|--------|--------|------|-------|
| IP Detection | ⬜ | | | |
| Manual Switching | ⬜ | | | |
| Booking URLs | ⬜ | | | |
| Price Display | ⬜ | | | |
| ZenProvider API | ⬜ | | | |
| End-to-End Flow | ⬜ | | | |
| Browser Compat | ⬜ | | | |
| Regression | ⬜ | | | |

**Final Approval**:
- [ ] Technical Lead Sign-Off: _________________ Date: _______
- [ ] QA Sign-Off: _________________ Date: _______
- [ ] Product Owner Sign-Off: _________________ Date: _______

## Notes & Observations

_Use this space to document any issues found, workarounds applied, or observations during testing:_

---

**Document Version**: 1.0
**Last Updated**: 2025-10-09
**Contact**: Jon Miller (Developer)
