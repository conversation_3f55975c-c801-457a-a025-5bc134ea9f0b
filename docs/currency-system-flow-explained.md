# Currency System Flow: From Database to Frontend Display

## Complete Flow Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                    ADMIN EDITS PRICE IN JOOMLA                   │
│          (Components → Zen Holidays → Edit Holiday)              │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
           ┌──────────────────────┐
           │  Admin changes GBP   │
           │  price: £1,995       │
           │  Clicks "Save"       │
           └──────────┬───────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│              JOOMLA EVENT: onHolidayPriceAfterSave               │
│         (Triggered automatically when price is saved)            │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│         FOREXPRICING PLUGIN: forexpricing.php (Line 53)          │
│   onHolidayPriceAfterSave($context, $id, $isNew, $data)         │
│                                                                  │
│   Checks: Is this the base currency (GBP)?                      │
│   If YES → calls ForexPricingHelper::createForexPrices()        │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│          FOREXPRICING HELPER: helper.php (Line 43)               │
│              createForexPrices(array $price)                     │
│                                                                  │
│  Step 1: Get all published currencies (excluding GBP)           │
│          → Queries: joomla_zencurrencies WHERE state=1          │
│          → Result: [USD]                                         │
│                                                                  │
│  Step 2: Get conversion rates for each currency                 │
│          → Queries: joomla_zencurrencyrates                     │
│          → Gets latest rate WHERE valid_from <= NOW()           │
│          → Result: USD rate = 1.27                              │
│                                                                  │
│  Step 3: Calculate converted values                             │
│          → GBP price: £1,995                                    │
│          → USD price: £1,995 × 1.27 = $2,533.65                 │
│          → Round to nearest whole number: $2,534                │
│          → Apply rounding increment (if set): $2,534            │
│                                                                  │
│  Step 4: Insert/Update USD price in database                    │
│          → INSERT INTO joomla_zenholidayprices                  │
│          → ON DUPLICATE KEY UPDATE (if already exists)          │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    DATABASE TABLE STRUCTURE                      │
│              joomla_zenholidayprices                            │
│                                                                  │
│  +────+─────────+─────────+───────────────+───────+──────+      │
│  | id | date_id | type_id | currency_code | value | state|      │
│  +────+─────────+─────────+───────────────+───────+──────+      │
│  | 1  | 123     | 1       | GBP           | 1995  | 1    |      │
│  | 2  | 123     | 1       | USD           | 2534  | 1    |      │
│  +────+─────────+─────────+───────────────+───────+──────+      │
│                                                                  │
│  ✅ Two price records for same date/type, different currencies  │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
                 [Time Passes]
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                 USER VISITS TRIP PAGE (FRONTEND)                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│      GEOIP DETECTION: ZenGeolocationHelper.php (Line 63)         │
│                  initializeCurrencyDetection()                   │
│                                                                  │
│  Step 1: Check if currency already set in session               │
│          → Checks: $_SESSION['__geoip_currency']                │
│                                                                  │
│  Step 2: If not set, get user's country by IP                   │
│          → Uses MaxMind GeoIP database                          │
│          → Example: IP ************ → United States             │
│                                                                  │
│  Step 3: Set currency based on country                          │
│          → US = USD                                             │
│          → All others = GBP                                     │
│                                                                  │
│  Step 4: Store in session                                       │
│          → $_SESSION['__geoip_currency'] = 'USD'                │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│         TEMPLATE LOADS: default_dates-prices.php                 │
│                                                                  │
│  Step 1: Get user's currency                                    │
│          → ZenSessionHelper::getUsersCurrencyCode()             │
│          → Returns: 'USD'                                       │
│                                                                  │
│  Step 2: Holiday model fetches ALL prices                       │
│          → Gets both GBP and USD prices from database           │
│          → $date->prices[1] = GBP price object                  │
│          → Could also have $date->prices[2] = USD price object  │
│                                                                  │
│  Step 3: Display logic filters by user's currency               │
│          → $datePrice = $date->prices[1] (in current code)      │
│          → Actually gets the price matching user's currency     │
│                                                                  │
│  Step 4: Show price with correct symbol                         │
│          → $datePrice->currency_symbol = "$"                    │
│          → $datePrice->value = 2534                             │
│          → Displays: "$2,534pp"                                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    BOOK NOW BUTTON CLICKED                       │
│                 (Lines 300-311 in template)                      │
│                                                                  │
│  Generates URL with:                                            │
│  - dateId: 123                                                  │
│  - priceTypeId: 1 (always 1 for standard price)                │
│  - currency: USD                                                │
│                                                                  │
│  Result: https://secure.evertrek.co.uk/booking/                │
│          ?sku=holiday:123-1&currency=USD                        │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│              BOOKING SYSTEM (Rezkit Integration)                 │
│        ZenProvider API: view.json.php (Lines 73-101)            │
│                bookMultiCurrencyHoliday()                       │
│                                                                  │
│  Step 1: Parse SKU from URL                                     │
│          → Extracts: dateId=123, priceTypeId=1                  │
│          → Extracts: currency=USD                               │
│                                                                  │
│  Step 2: Find matching price in database                        │
│          → SELECT id FROM joomla_zenholidayprices               │
│          → WHERE date_id=123 AND type_id=1                      │
│          → AND currency_code='USD'                              │
│          → Result: priceId = 2                                  │
│                                                                  │
│  Step 3: Create booking reservation                             │
│          → Uses priceId=2 (USD price)                          │
│          → Creates reservation record                           │
│          → Returns booking reference                            │
└─────────────────────────────────────────────────────────────────┘
```

---

## Database Tables Involved

### 1. `joomla_zencurrencies`
**Purpose**: Defines which currencies are available

```sql
CREATE TABLE joomla_zencurrencies (
    id INT PRIMARY KEY,
    code VARCHAR(3),      -- 'USD', 'GBP', etc.
    symbol VARCHAR(10),   -- '$', '£', etc.
    title VARCHAR(100),   -- 'United States Dollar'
    state TINYINT         -- 1 = published, 0 = unpublished
);
```

**Example Data**:
```
+----+------+--------+-----------------------+-------+
| id | code | symbol | title                 | state |
+----+------+--------+-----------------------+-------+
| 1  | GBP  | £      | British Pound         | 1     |
| 2  | USD  | $      | United States Dollar  | 1     |
+----+------+--------+-----------------------+-------+
```

---

### 2. `joomla_zencurrencyrates`
**Purpose**: Stores exchange rates between currencies

```sql
CREATE TABLE joomla_zencurrencyrates (
    id INT PRIMARY KEY,
    from VARCHAR(3),           -- 'GBP'
    to VARCHAR(3),             -- 'USD'
    rate DECIMAL(10,4),        -- 1.2700 (£1 = $1.27)
    rounding_increment INT,    -- 1 = round to whole dollar
    valid_from DATE,           -- When this rate became valid
    state TINYINT
);
```

**Example Data**:
```
+----+------+----+--------+--------------------+------------+-------+
| id | from | to | rate   | rounding_increment | valid_from | state |
+----+------+----+--------+--------------------+------------+-------+
| 1  | GBP  | USD| 1.2700 | 1                  | 2025-10-01 | 1     |
+----+------+----+--------+--------------------+------------+-------+
```

**Note**: The rate is the **multiplier** to convert from GBP to USD:
- `GBP price × rate = USD price`
- Example: £1,995 × 1.27 = $2,533.65 → rounds to $2,534

---

### 3. `joomla_zenholidayprices`
**Purpose**: Stores actual prices for each date/currency combination

```sql
CREATE TABLE joomla_zenholidayprices (
    id INT PRIMARY KEY,
    date_id INT,               -- FK to joomla_zenholidaydates
    type_id INT,               -- FK to joomla_zenholidaypricetypes (1=adult)
    currency_code VARCHAR(3),  -- 'GBP' or 'USD'
    value DECIMAL(10,2),       -- Price amount (1995.00 or 2534.00)
    previous_value DECIMAL,    -- Previous price (for "was X now Y" display)
    spaces INT,                -- Available spaces at this price
    state TINYINT,             -- 1 = published
    created DATETIME,
    modified_at DATETIME,
    UNIQUE KEY (date_id, type_id, currency_code)  -- One price per date/type/currency
);
```

**Example Data**:
```
+----+---------+---------+---------------+---------+-------+-------+
| id | date_id | type_id | currency_code | value   | spaces| state |
+----+---------+---------+---------------+---------+-------+-------+
| 1  | 123     | 1       | GBP           | 1995.00 | 10    | 1     |
| 2  | 123     | 1       | USD           | 2534.00 | 10    | 1     |
+----+---------+---------+---------------+---------+-------+-------+
```

**Key Point**: **Two separate price records** for the same date/type, just different currencies!

---

## How Currency Selection Works (Frontend)

### Detection Priority (from highest to lowest):

```
1. URL Parameter
   ?currency=USD
   ↓
2. Session Variable
   $_SESSION['__geoip_currency']
   ↓
3. Cookie
   $_COOKIE['locale'] → "en-GB|USD"
   ↓
4. GeoIP Lookup
   IP → Country → Currency
   (US = USD, others = GBP)
   ↓
5. Component Default
   com_zenadmin → geoip_default_currency → 'USD'
```

**Implementation**: `ZenSessionHelper::getUsersCurrencyCode()` (lines 79-117)

```php
public static function getUsersCurrencyCode()
{
    // Step 1: Check URL parameter
    $cc = JFactory::getApplication()->input->getString('currency', false);
    if ($cc) return $cc;

    // Step 2: Check session
    $code = $session->get('__geoip_currency', false);
    if ($code) return $code;

    // Step 3: Check GeoIP (includes cookie check internally)
    $geo = new ZenGeolocationHelper();
    $code = $geo->getCountry()->sales_currency_code;
    if ($code) return $code;

    // Step 4: Default fallback
    $params = JComponentHelper::getParams('com_zenadmin');
    $code = $params->get('geoip_default_currency', 'USD');

    // Step 5: Store in session for next time
    $session->set('__geoip_currency', $code);

    return $code;
}
```

---

## How Prices Are Displayed (Frontend)

### Holiday Model Loads ALL Prices

When you view a trip page, the holiday model loads **ALL price records** for each date:

```php
// In Holiday Model
$query = "
    SELECT p.*
    FROM joomla_zenholidayprices p
    WHERE p.date_id = $dateId
    AND p.state = 1
    ORDER BY p.type_id, p.currency_code
";
```

**Result**: Each `$date` object has a `prices` array:
```php
$date->prices = [
    1 => stdClass {
        id: 1,
        currency_code: 'GBP',
        currency_symbol: '£',
        value: 1995
    },
    2 => stdClass {
        id: 2,
        currency_code: 'USD',
        currency_symbol: '$',
        value: 2534
    }
];
```

### Template Filters by User's Currency

**Current Code Issue** (default_dates-prices.php:239):
```php
$datePrice = $date->prices[1];  // Always gets first price (GBP)
```

**What it SHOULD do** (needs fixing):
```php
// Get user's currency
$userCurrency = ZenSessionHelper::getUsersCurrencyCode();

// Find matching price
$datePrice = null;
foreach ($date->prices as $price) {
    if ($price->currency_code === $userCurrency) {
        $datePrice = $price;
        break;
    }
}

// Fallback to first price if not found
if (!$datePrice) {
    $datePrice = $date->prices[1];
}
```

### Display Price with Symbol

```php
echo $datePrice->currency_symbol . number_format($datePrice->value) . "pp";
// USD: "$2,534pp"
// GBP: "£1,995pp"
```

---

## ForexPricing Plugin Workflow (Detailed)

### Trigger Event
```
Admin saves price → Joomla fires onHolidayPriceAfterSave event
```

### Plugin Processing

**File**: `plugins/mrzen/forexpricing/forexpricing.php`

```php
// Line 53: Event handler
public function onHolidayPriceAfterSave($context, $id, $isNew, $data)
{
    $params = JComponentHelper::getParams('com_zenadmin');
    $baseCurrency = $params->get('base_currency', 'USD'); // Actually 'GBP' in config

    // Only process base currency prices
    if ($data['currency_code'] === $baseCurrency) {
        $createdPrices = ForexPricingHelper::createForexPrices($data);

        // Show success message in admin
        if ($createdPrices) {
            $message = 'Created/Updated ' . count($createdPrices) . ' forex prices';
            // Shows: "Created USD → 2534"
        }
    }
}
```

### Helper Processing

**File**: `plugins/mrzen/forexpricing/helper.php`

**Step 1**: Get Currencies (Line 142)
```php
private static function _getCurrencies($exclude)
{
    // Get all published currencies EXCEPT the base currency
    $query = "
        SELECT * FROM joomla_zencurrencies
        WHERE state = 1
        AND code <> '$exclude'  // Excludes 'GBP'
    ";
    // Returns: [USD currency object]
}
```

**Step 2**: Get Conversion Rates (Line 118)
```php
private static function _getConversionRates($from, array $to)
{
    // Gets latest rate for each currency pair
    $query = "
        SELECT cr.*
        FROM joomla_zencurrencyrates cr
        JOIN (
            SELECT `from`, `to`, MAX(valid_from) AS valid_from
            FROM joomla_zencurrencyrates
            WHERE valid_from <= NOW()
            GROUP BY `from`, `to`
        ) AS crp ON crp.from = cr.from
                AND crp.to = cr.to
                AND crp.valid_from = cr.valid_from
    ";
    // Returns: ['USD' => {rate: 1.27, rounding: 1}]
}
```

**Step 3**: Calculate Converted Values (Line 73)
```php
private static function _getConvertedValues($price, $conversionRates)
{
    foreach ($conversionRates as $code => $conversion) {
        $rate = $conversion->rate;           // 1.27
        $rounding = $conversion->rounding;   // 1

        // Convert
        $value = $price['value'] * $rate;    // 1995 × 1.27 = 2533.65

        // Round to whole number
        $value = round($value);              // 2534

        // Apply rounding increment
        if ($rounding >= 1) {
            if ($value % $rounding !== 0) {
                // Round up to nearest increment
                $value += ($rounding - ($value % $rounding));
            }
        }

        $convertedValues[$code] = ['value' => $value];
    }
    return $convertedValues;
}
```

**Step 4**: Insert/Update Database (Line 56)
```php
public static function createForexPrices(array $price)
{
    $query = "
        INSERT INTO joomla_zenholidayprices
        (date_id, type_id, currency_code, value, previous_value, state)
        VALUES
        ($price[date_id], $price[type_id], 'USD', 2534, 0, 1)
        ON DUPLICATE KEY UPDATE
            value = VALUES(value),
            previous_value = VALUES(previous_value),
            modified_at = NOW()
    ";

    // If USD price already exists → UPDATE
    // If USD price doesn't exist → INSERT
}
```

---

## Common Scenarios

### Scenario 1: New Trip, New Dates
1. Admin creates holiday with 5 dates
2. Admin adds GBP prices for all 5 dates
3. **Plugin runs 5 times** (once per date)
4. Creates **5 USD price records**
5. Database now has **10 price records** (5 GBP + 5 USD)

### Scenario 2: Price Update
1. Admin edits existing GBP price: £1,995 → £1,895
2. Clicks "Save"
3. **Plugin runs once**
4. Updates USD price: $2,534 → $2,408
5. Database **updates 1 USD record**

### Scenario 3: Exchange Rate Change
1. Admin updates USD exchange rate: 1.27 → 1.30
2. **Plugin does NOT automatically run**
3. Must either:
   - Edit each GBP price (slow)
   - OR run bulk update via admin button: "Update all Forex Prices"
   - OR run SQL script to recalculate all

### Scenario 4: User Switches Currency
1. User visits trip page with US IP → detects USD
2. Prices show in USD: "$2,534"
3. User clicks currency switcher → selects GBP
4. Page reloads with `?currency=GBP` in URL
5. Session updates: `$_SESSION['__geoip_currency'] = 'GBP'`
6. Prices show in GBP: "£1,995"
7. Currency persists across navigation

---

## Key Integration Points

### 1. Template → Session Helper
```php
// default_dates-prices.php
$userCurrency = ZenSessionHelper::getUsersCurrencyCode();
```

### 2. Session Helper → Geolocation Helper
```php
// ZenSessionHelper.php
$geo = new ZenGeolocationHelper();
$code = $geo->getCountry()->sales_currency_code;
```

### 3. Geolocation Helper → Session
```php
// ZenGeolocationHelper.php
$session->set('__geoip_currency', $currency);
```

### 4. Template → Booking URL
```php
// default_dates-prices.php (NEW CODE)
$url = $bookingPrefix . '?sku=holiday:' . $date->id . '-1&currency=' . $userCurrency;
```

### 5. ZenProvider API → Database
```php
// view.json.php
$priceId = $db->query("
    SELECT id FROM joomla_zenholidayprices
    WHERE date_id = $dateId
    AND type_id = $priceTypeId
    AND currency_code = '$currency'
");
```

---

## Testing the Complete Flow

### End-to-End Test

1. **Database Setup**:
```sql
-- Set exchange rate
INSERT INTO joomla_zencurrencyrates (from, to, rate, rounding_increment, valid_from, state)
VALUES ('GBP', 'USD', 1.27, 1, NOW(), 1);

-- Enable USD
UPDATE joomla_zencurrencies SET state = 1, symbol = '$' WHERE code = 'USD';
```

2. **Generate Prices**:
- Go to admin
- Edit any holiday
- Change a GBP price
- Save → Plugin auto-generates USD price

3. **Verify Database**:
```sql
SELECT date_id, currency_code, value
FROM joomla_zenholidayprices
WHERE date_id = 123
ORDER BY currency_code;

-- Should show:
-- date_id | currency_code | value
-- 123     | GBP           | 1995
-- 123     | USD           | 2534
```

4. **Test Frontend**:
- Visit trip page
- Check debug panel shows your currency
- Verify prices display in correct currency
- Switch currency via switcher
- Verify URL changes: `?currency=USD` or `?currency=GBP`
- Check "Book Now" URL includes currency parameter

5. **Test Booking**:
- Click "Book Now"
- URL should be: `?sku=holiday:123-1&currency=USD`
- ZenProvider API should find correct price (USD version)
- Booking proceeds with USD price

---

## Troubleshooting

### USD Prices Not Created
**Check**:
1. Plugin enabled: Extensions → Plugins → "forexpricing"
2. Base currency set: Components → Zen Admin → Configuration → `base_currency = 'GBP'`
3. Exchange rate exists: Check `joomla_zencurrencyrates` table
4. USD currency published: Check `joomla_zencurrencies` WHERE code='USD' AND state=1

### Wrong USD Prices
**Fix**:
1. Check exchange rate is correct (~1.27, not 0.79)
2. Recalculate all: Run SQL bulk update or use admin button
3. Clear Joomla cache

### Currency Not Switching
**Check**:
1. Session working: PHP session enabled
2. GeoIP plugin enabled: Extensions → Plugins → "geolocate"
3. Currency switcher published: Extensions → Modules → mod_zencurrencyswitcher
4. Template includes switcher: Check module position

---

## Summary

**The Complete Journey**:
1. **Admin edits GBP price** → Saves
2. **Plugin catches save event** → Calculates USD equivalent
3. **Plugin writes USD price** → Database (separate record)
4. **User visits page** → GeoIP detects country
5. **Session stores currency** → USD or GBP
6. **Template loads prices** → Gets both GBP and USD from DB
7. **Template filters by user currency** → Shows only USD or GBP
8. **User clicks Book Now** → URL includes currency parameter
9. **ZenProvider API** → Finds matching price record
10. **Booking proceeds** → With correct currency price

**Key Takeaway**: The system maintains **parallel price records** in the database - one for each currency. The ForexPricing plugin keeps them in sync, and the frontend filters which one to show based on user's detected/selected currency.
