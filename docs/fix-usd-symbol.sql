-- Fix USD Currency Symbol
-- Run this SQL to update the USD symbol to $ instead of US$

-- Check current USD currency record
SELECT id, code, symbol, title, state
FROM joomla_zencurrencies
WHERE code = 'USD';

-- Update USD symbol to just $
UPDATE joomla_zencurrencies
SET symbol = '$'
WHERE code = 'USD';

-- Verify the change
SELECT id, code, symbol, title, state
FROM joomla_zencurrencies
WHERE code = 'USD';

-- Expected output:
-- id | code | symbol | title               | state
-- X  | USD  | $      | United States Dollar| 1
