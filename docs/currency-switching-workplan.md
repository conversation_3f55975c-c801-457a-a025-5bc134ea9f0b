# Currency Switching Workplan (Comparison Matrix Project)

This document outlines your step-by-step tasks to be ready by Friday (no weekend work), and how those tasks depend on Rezkit/Mark. It also includes a dependency Gantt so you can visualize your work alongside <PERSON>z<PERSON><PERSON>.

## Assumptions
- Today: Thursday, 2025-10-09
- Your goal: All your implementation and pre-go-live checks ready by Friday; avoid weekend work
- Go-live: Coordinated with Rezkit early/mid next week (pending <PERSON>’s timing)

## Urgent tasks (complete by Friday)
1. [Critical] **Update booking URL structure**
   - Change from holiday:priceId to holiday:dateId-priceTypeId&currency=XXX
   - Ensure currency parameter is appended on every booking link
2. [High] **Implement/finish IP-based currency detection**
   - Default USD for US visitors; GBP for others; persist choice in session. Not required at go-live (can default everyone to GBP until USD is loaded).
3. [Medium] **Add/finish currency switcher UI**
   - Visible in header/footer; polished UX
   - Optional — not required if only IP-based detection is used.
4. [High] **Verify USD and GBP price display**
   - Symbols, formatting, payment plan calculations
5. [High] **Test currency switching end-to-end**
   - IP detection, manual switch, session persistence, booking URL generation
6. [High] **Pre-go-live testing**
   - Full regression on staging
7. [High] **Coordination: lock in go-live window with Mark**
   - Confirm the hour where your URL change deploy aligns with Rezkit’s live SKU migration

## Key dependencies on Rezkit/Mark
- Pre-requisite booking journey updates (95% complete per Mark)
- Test SKU migration on staging (in progress)
- Outstanding support tickets: file upload & Alpkit link
- Live SKU migration (must be simultaneous with your URL change deploy)

## Schedule to finish by Friday (no weekend work)

| Step | Task | Owner | Priority | Est. Time | When | Dependencies/Notes |
|------|------|-------|----------|-----------|------|---------------------|
| 1 | Enable USD currency in CMS (verify) | Jon | Low | 0.5h | Thu 2025-10-09 | Verify #__zencurrencies |
| 2 | Implement IP-based currency detection | Jon | High | 1.5h | Thu 2025-10-09 | Depends: Step 1; Not required at go-live (default to GBP acceptable) |
| 3 | Start currency switcher UI | Jon | Medium | 1.0h | Thu 2025-10-09 | Optional — not required for go-live |
| 4 | Finish currency switcher UI | Jon | Medium | 0.5h | Fri 2025-10-10 | Depends: Step 3; Optional — not required for go-live |
| 5 | Update Book Now URLs to new format | Jon | Critical | 2.5h | Fri 2025-10-10 | Depends: Step 4; PriceTypeId will always be 1 |
| 6 | Add currency parameter to all booking links | Jon | High | 1.0h | Fri 2025-10-10 | Depends: Step 5; If omitted, Rezkit falls back to GBP |
| 7 | Update ZenProvider API for multi-currency | Jon | Medium | 1.0h | Fri 2025-10-10 | Depends: Step 6 |
| 8 | Load/input USD prices | Evertrek Team | High | 1.0h | Fri 2025-10-10 | Coordination |
| 9 | Verify USD/GBP price display | Jon | High | 1.5h | Fri 2025-10-10 | Depends: Step 8 |
| 10 | Test currency switching end-to-end | Jon | High | 1.0h | Fri 2025-10-10 | After Steps 4–7 |
| 11 | Pre-go-live testing & regression (staging) | Jon | High | 1.0h | Fri 2025-10-10 | After Step 10 and Step 7 |
| 12 | Establish go-live timing window | Jon + Mark | High | 0.5h | Fri 2025-10-10 | Coordination with Rezkit |

## External (Next Week) — Rezkit

| Step | Task | Owner | Priority | When (tentative) | Dependencies/Notes |
|------|------|-------|----------|-------------------|---------------------|
| A1 | Resolve outstanding support tickets (file upload, Alpkit link) | Mark (Rezkit) | Medium | Mon 2025-10-13 | Prefer resolved before A3 |
| A2 | Test SKU migration (staging) | Mark (Rezkit) | High | Fri 2025-10-10 | Migration tool ready; validates new IDs |
| A3 | Update and test booking journey & my account | Mark (Rezkit) | High | Mon 2025-10-13 | Depends: A2 |
| A4 | Live SKU migration (coordinated) | Mark (Rezkit) | Critical | Tue 2025-10-14 | Depends: A2–A3; run simultaneously with website URL change |
| A5 | Coordinate go-live hour | Jon + Mark | High | Fri 2025-10-10 | Required for A4 |

## Definition of done (your side)
- All booking URLs follow new pattern and include currency param
- Detection + switcher fully working and persisting choice
- USD and GBP displays correct across views and payment plans
- Staging regression passes
- Go-live window agreed with Mark

## ZenProvider API changes (details)

- **Goal:** Ensure booking URLs use the new SKU format and carry an explicit currency parameter.
- **Files to edit/check:**
    - `components/com_zenprovider/views/book/view.json.php` (method: `bookMultiCurrencyHoliday`)
- **Required changes:**
    - Build `sku` as `holiday:{dateId}-1` where `PriceTypeId` is always `1` (per Rezkit notes).
    - Append `&currency={GBP|USD}` sourced from session/user setting. Force uppercase and allow-list only `GBP`, `USD`.
    - Default to `GBP` if currency is missing/invalid (Rezkit also falls back to `GBP`; enforce server-side too).
    - Validate `dateId` (numeric, present) before constructing the URL. Return a safe error or log if invalid.
    - Sanitize all inputs used in the URL assembly and avoid passing unexpected params.
- **Optional safety (transition/rollback):**
    - Temporary compatibility: accept legacy `holiday:{priceId}` inputs and either map to `dateId` if available or reject gracefully with logging. Remove once go-live is stable.
- **Frontend coordination (where links originate):**
    - `templates/zenbase/html/com_zenholidays/holiday/default_dates-prices.php` — ensure links pass `dateId` (not `priceId`) and include `&currency={GBP|USD}`.
- **Test checklist (staging):**
    - Generated GBP URL: `https://secure.evertrek.co.uk/booking/?sku=holiday:{dateId}-1&currency=GBP`
    - Generated USD URL: `https://secure.evertrek.co.uk/booking/?sku=holiday:{dateId}-1&currency=USD`
    - Omit currency → Rezkit and API default to `GBP` and booking succeeds.
    - Invalid currency (e.g., `EUR`) → API coerces to `GBP`; logs event.
    - Invalid/missing `dateId` → API responds safely; no fatal errors.

## Dependency Gantt (Jon vs Rezkit/Mark)
```mermaid
gantt
    title Currency Switching Jon vs Rezkit Mark No Weekend Work for Jon
    dateFormat YYYY-MM-DD
    axisFormat %m/%d
    excludes weekends
    
    section Jon You
    Enable USD verification           :done, jon_usd, 2025-10-09, 1d
    IP Currency Detection             :active, jon_ip, 2025-10-09, 1d
    Currency Switcher UI              :jon_ui, 2025-10-10, 1d
    Update Book Now URLs              :crit, jon_urls, 2025-10-10, 1d
    Add Currency Params               :crit, jon_params, after jon_urls, 1d
    Update ZenProvider API            :jon_api, after jon_params, 1d
    Verify Price Display              :jon_display, after jon_api, 1d
    Test Currency Switching           :jon_test, after jon_display, 1d
    Pre-Go-Live Testing               :jon_pregolive, after jon_test, 1d

    section Rezkit Mark
    Pre-req Booking Updates           :active, rk_prereq, 2025-10-01, 9d
    Test SKU Migration                :rk_testsku, 2025-10-10, 1d
    Update booking journey            :rk_update, after rk_testsku, 1d
    Support Tickets                   :rk_support, 2025-10-09, 2d
    Live SKU Migration                :crit, rk_livesku, 2025-10-14, 1d

    section Coordination
    Agree Go-Live Window              :coord_window, 2025-10-10, 1d
    Coordinated Deployment            :crit, coord_deploy, after rk_livesku, 1d

```

## Notes
- Live migration date is indicative (Tue 14th) to reflect “early/mid next week”; confirm with Mark.
- Currency switcher is optional if you rely solely on IP-based currency detection.
- IP-based currency detection and USD enabling are not required at the moment of the live SKU update; you can default to GBP and enable later.
- PriceTypeId will always be 1 in the new Book Now URL format; currency defaults to GBP if omitted.
- Coordinated Deployment occurs when both Jon pre-go-live is complete and Rezkit live SKU migration is scheduled.
- If any Rezkit dependency slips, keep your code ready but hold the URL change deployment until the synchronized window.

## How to use this plan
- Work through the Thursday–Friday plan above.
- Keep the CSV schedule (currency-switching-schedule.csv) updated with actuals.
- As soon as staging is green, lock in the go-live window with Mark and prepare a short rollback note (e.g., revert commit or feature flag) just in case.
