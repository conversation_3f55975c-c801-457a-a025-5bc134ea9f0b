-- ============================================================================
-- CHECK USD PRICE GENERATION STATUS
-- ============================================================================
-- Run this to diagnose why 0 rows were affected
-- ============================================================================

-- Step 1: Check if USD currency exists
-- ============================================================================
SELECT '=== 1. USD Currency Setup ===' as check_step;

SELECT * FROM ev_zencurrencies WHERE code = 'USD';

-- Expected: Should return 1 row with USD currency details
-- If empty: You need to create USD currency first

-- Step 2: Check if USD exchange rate exists
-- ============================================================================
SELECT '=== 2. USD Exchange Rate ===' as check_step;

SELECT * FROM ev_zencurrencyrates
WHERE currency_code = 'USD'
ORDER BY valid_from DESC
LIMIT 5;

-- Expected: Should show USD rate (e.g., 1.27)
-- If empty: You need to add exchange rate first

-- Step 3: Check GBP prices exist
-- ============================================================================
SELECT '=== 3. GBP Prices Count ===' as check_step;

SELECT COUNT(*) as gbp_price_count
FROM ev_zenholidayprices
WHERE currency_code = 'GBP' AND state = 1;

-- Expected: Should be > 0 (e.g., 1000+)
-- If 0: No GBP prices exist to convert from!

-- Step 4: Check USD prices exist
-- ============================================================================
SELECT '=== 4. USD Prices Count ===' as check_step;

SELECT COUNT(*) as usd_price_count
FROM ev_zenholidayprices
WHERE currency_code = 'USD' AND state = 1;

-- Expected after script: Should match GBP count
-- If already has count: Script would skip (NOT EXISTS prevents duplicates)

-- Step 5: Check all currencies in database
-- ============================================================================
SELECT '=== 5. All Currencies in Price Table ===' as check_step;

SELECT
    currency_code,
    COUNT(*) as count,
    MIN(value) as min_price,
    MAX(value) as max_price
FROM ev_zenholidayprices
WHERE state = 1
GROUP BY currency_code;

-- This shows ALL currencies currently in the system

-- Step 6: Sample of GBP prices (first 5)
-- ============================================================================
SELECT '=== 6. Sample GBP Prices ===' as check_step;

SELECT
    id,
    date_id,
    type_id,
    currency_code,
    value,
    spaces,
    state
FROM ev_zenholidayprices
WHERE currency_code = 'GBP'
LIMIT 5;

-- Step 7: Check if USD prices ALREADY exist for those same date_id/type_id
-- ============================================================================
SELECT '=== 7. Do USD Prices Already Exist for Sample Dates? ===' as check_step;

SELECT
    id,
    date_id,
    type_id,
    currency_code,
    value,
    state
FROM ev_zenholidayprices
WHERE currency_code = 'USD'
  AND date_id IN (
    SELECT date_id
    FROM ev_zenholidayprices
    WHERE currency_code = 'GBP'
    LIMIT 5
  )
LIMIT 10;

-- If this returns rows: USD prices ALREADY EXIST (script won't duplicate)

-- Step 8: Check for GBP prices WITHOUT USD equivalent
-- ============================================================================
SELECT '=== 8. GBP Prices Missing USD Equivalent ===' as check_step;

SELECT COUNT(*) as missing_usd_count
FROM ev_zenholidayprices gbp
WHERE gbp.currency_code = 'GBP'
  AND gbp.state = 1
  AND NOT EXISTS (
    SELECT 1
    FROM ev_zenholidayprices usd
    WHERE usd.date_id = gbp.date_id
      AND usd.type_id = gbp.type_id
      AND usd.currency_code = 'USD'
  );

-- Expected before script: Should be > 0
-- Expected after script: Should be 0
-- If 0 before script: USD prices already exist!

-- Step 9: Detailed diagnosis
-- ============================================================================
SELECT '=== 9. DIAGNOSIS ===' as check_step;

SELECT
    CASE
        WHEN (SELECT COUNT(*) FROM ev_zencurrencies WHERE code = 'USD') = 0
            THEN '❌ PROBLEM: USD currency not set up in ev_zencurrencies table'
        WHEN (SELECT COUNT(*) FROM ev_zencurrencyrates WHERE currency_code = 'USD') = 0
            THEN '❌ PROBLEM: USD exchange rate not set in ev_zencurrencyrates table'
        WHEN (SELECT COUNT(*) FROM ev_zenholidayprices WHERE currency_code = 'GBP' AND state = 1) = 0
            THEN '❌ PROBLEM: No GBP prices exist to convert from'
        WHEN (SELECT COUNT(*) FROM ev_zenholidayprices gbp
              WHERE gbp.currency_code = 'GBP' AND gbp.state = 1
              AND NOT EXISTS (
                  SELECT 1 FROM ev_zenholidayprices usd
                  WHERE usd.date_id = gbp.date_id
                    AND usd.type_id = gbp.type_id
                    AND usd.currency_code = 'USD'
              )) = 0
            THEN '✅ SUCCESS: All GBP prices already have USD equivalents! (0 rows affected is correct)'
        ELSE '⚠️ UNCLEAR: Check individual steps above'
    END as diagnosis;

-- ============================================================================
-- INTERPRETATION:
-- ============================================================================
-- "0 rows affected" can mean:
--
-- 1. ✅ SUCCESS - USD prices already exist (script has NOT EXISTS check)
-- 2. ❌ ERROR - USD currency not set up
-- 3. ❌ ERROR - USD exchange rate not set
-- 4. ❌ ERROR - No GBP prices to convert from
--
-- Run this diagnostic script to find out which!
-- ============================================================================
