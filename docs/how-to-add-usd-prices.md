# How to Add USD Prices to Products

## Quick Answer

**You DON'T manually add USD prices!** The ForexPricing plugin automatically generates them when you edit a GBP price.

## Step-by-Step: Generate USD Prices

### Method 1: Automatic Generation (Recommended)

1. **Log into Joomla Admin**
   - Go to: `https://yourdomain.com/administrator`

2. **Navigate to Holidays**
   - Click: **Components** → **Zen Holidays** → **Holidays**

3. **Edit ANY Holiday**
   - Click on any holiday in the list
   - Scroll to the **Dates & Prices** section

4. **Edit ANY GBP Price**
   - Find any existing price (should be in GBP)
   - Change the value slightly (e.g., £1,995 → £1,996)
   - Or just click in the price field and press Save without changing

5. **Save the Holiday**
   - Click **Save** or **Save & Close**

6. **ForexPricing Plugin Automatically Runs**
   - The plugin detects the GBP price change
   - Calculates USD equivalent using exchange rate
   - Creates/updates USD price record in database
   - Takes ~1-2 seconds per price

7. **Verify USD Price Created**
   - Check database (see Method 2 below)
   - Or view the holiday on frontend and switch currency

### Method 2: Bulk Generate All USD Prices

If you need to generate USD prices for ALL holidays at once:

#### Option A: Via Database Script

**Run this SQL** (creates USD prices for all existing GBP prices):

```sql
-- Step 1: Get the current USD exchange rate
SELECT @usd_rate := rate
FROM joomla_zencurrencyrates
WHERE currency_code = 'USD'
  AND valid_from <= NOW()
ORDER BY valid_from DESC
LIMIT 1;

-- Step 2: Insert USD prices for all GBP prices that don't have USD equivalent
INSERT INTO joomla_zenholidayprices (date_id, type_id, currency_code, value, spaces, state, created, created_by)
SELECT
    p.date_id,
    p.type_id,
    'USD' as currency_code,
    ROUND(p.value * @usd_rate, 0) as value,
    p.spaces,
    p.state,
    NOW() as created,
    0 as created_by
FROM joomla_zenholidayprices p
WHERE p.currency_code = 'GBP'
  AND NOT EXISTS (
    SELECT 1 FROM joomla_zenholidayprices p2
    WHERE p2.date_id = p.date_id
      AND p2.type_id = p.type_id
      AND p2.currency_code = 'USD'
  );

-- Step 3: Verify USD prices were created
SELECT
    COUNT(CASE WHEN currency_code = 'GBP' THEN 1 END) as gbp_prices,
    COUNT(CASE WHEN currency_code = 'USD' THEN 1 END) as usd_prices
FROM joomla_zenholidayprices;
```

#### Option B: Via PHP Script

Create a file: `/administrator/tmp/generate_usd_prices.php`

```php
<?php
define('_JEXEC', 1);
define('JPATH_BASE', dirname(__FILE__) . '/../..');

require_once JPATH_BASE . '/includes/defines.php';
require_once JPATH_BASE . '/includes/framework.php';

$app = JFactory::getApplication('site');

$db = JFactory::getDbo();

// Get USD exchange rate
$query = $db->getQuery(true);
$query->select('rate')
    ->from('#__zencurrencyrates')
    ->where('currency_code = ' . $db->quote('USD'))
    ->where('valid_from <= NOW()')
    ->order('valid_from DESC');
$db->setQuery($query, 0, 1);
$usdRate = (float)$db->loadResult();

if (!$usdRate) {
    die("ERROR: No USD exchange rate found!\n");
}

echo "USD Exchange Rate: $usdRate\n";

// Get all GBP prices without USD equivalent
$query = $db->getQuery(true);
$query->select('p.*')
    ->from('#__zenholidayprices AS p')
    ->where('p.currency_code = ' . $db->quote('GBP'))
    ->where('NOT EXISTS (
        SELECT 1 FROM #__zenholidayprices p2
        WHERE p2.date_id = p.date_id
          AND p2.type_id = p.type_id
          AND p2.currency_code = ' . $db->quote('USD') . '
    )');
$db->setQuery($query);
$gbpPrices = $db->loadObjectList();

echo "Found " . count($gbpPrices) . " GBP prices without USD equivalent\n";

$created = 0;
foreach ($gbpPrices as $gbpPrice) {
    $usdValue = round($gbpPrice->value * $usdRate, 0);

    $usdPrice = [
        'date_id' => $gbpPrice->date_id,
        'type_id' => $gbpPrice->type_id,
        'currency_code' => 'USD',
        'value' => $usdValue,
        'spaces' => $gbpPrice->spaces,
        'state' => $gbpPrice->state,
        'created' => date('Y-m-d H:i:s'),
        'created_by' => 0
    ];

    $db->insertObject('#__zenholidayprices', (object)$usdPrice);
    $created++;

    if ($created % 100 == 0) {
        echo "Created $created USD prices...\n";
    }
}

echo "\nDONE! Created $created USD prices.\n";
```

Then run:
```bash
php /path/to/joomla/administrator/tmp/generate_usd_prices.php
```

### Method 3: Trigger ForexPricing Plugin Manually

**Enable Plugin** (if not already):
1. Go to: **Extensions** → **Plugins**
2. Search: "forexpricing"
3. Status: **Enabled** ✅

**Configure Plugin**:
1. Click on the plugin name
2. Check settings:
   - **Base Currency**: GBP
   - **Target Currencies**: USD
   - **Rounding**: 1 (rounds to nearest whole number)
3. **Save & Close**

**Trigger Generation**:
- Edit any holiday price → Save
- Plugin runs automatically on save

---

## Verify USD Prices Exist

### Via Database

```sql
-- Check total prices by currency
SELECT
    currency_code,
    COUNT(*) as price_count,
    MIN(value) as min_price,
    MAX(value) as max_price,
    AVG(value) as avg_price
FROM joomla_zenholidayprices
WHERE state = 1
GROUP BY currency_code;

-- Expected output:
-- currency_code | price_count | min_price | max_price | avg_price
-- GBP           | 1234        | 500       | 5000      | 1800
-- USD           | 1234        | 625       | 6250      | 2250
```

### Via Frontend

1. **Visit any trip page**
2. **Switch currency to USD** (using currency switcher)
3. **Check prices display in USD** ($ symbol, higher numbers)
4. **Right-click "Book Now"** → Copy Link
5. **Verify URL** contains: `&currency=USD`

### Via Admin

1. Go to: **Components** → **Zen Holidays** → **Holidays**
2. Click on any holiday
3. Scroll to **Dates & Prices**
4. Click **Edit** on a date
5. You should see **TWO price records**:
   - One with `GBP` and £ symbol
   - One with `USD` and $ symbol

---

## Set/Update Exchange Rate

### Via Admin Interface

1. **Navigate to Currency Rates**
   - Go to: **Components** → **Zen Admin** → **Currency Rates**

2. **Add New Rate** (or edit existing)
   - **Currency Code**: USD
   - **Rate**: 0.79 (example: 1 USD = 0.79 GBP, or GBP to USD multiply by ~1.27)
   - **Valid From**: Today's date (YYYY-MM-DD)
   - **Rounding Increment**: 1 (whole dollars)
   - **State**: Published

3. **Save & Close**

**Note**: The rate is **GBP → USD conversion multiplier**
- If £1 = $1.27, then rate = 1.27
- If you want $2,000 for a £1,575 trip: 1575 × 1.27 = 2,000

### Calculate Correct Rate

**Formula**: `USD Rate = 1 / GBP Rate`

**Example**:
- Current GBP/USD = 0.79 (£1 = $0.79)
- To convert GBP → USD: multiply by (1 / 0.79) = 1.27
- So a £1,000 trip = $1,270

**Check Current Rate**:
```bash
curl -s "https://api.exchangerate-api.com/v4/latest/GBP" | grep -o '"USD":[0-9.]*'
# Output: "USD":1.27
```

Or visit: https://www.xe.com/currencyconverter/convert/?Amount=1&From=GBP&To=USD

---

## Troubleshooting

### USD Prices Not Showing

**Check 1**: USD currency is enabled
```sql
SELECT * FROM joomla_zencurrencies WHERE code = 'USD';
-- state should be 1
```

**Check 2**: Exchange rate exists
```sql
SELECT * FROM joomla_zencurrencyrates
WHERE currency_code = 'USD'
ORDER BY valid_from DESC LIMIT 1;
-- Should return a recent rate
```

**Check 3**: ForexPricing plugin is enabled
- Go to: **Extensions** → **Plugins** → Search "forexpricing"
- Status: **Enabled** ✅

**Check 4**: USD prices exist in database
```sql
SELECT COUNT(*) FROM joomla_zenholidayprices WHERE currency_code = 'USD';
-- Should be > 0
```

### Prices Are Wrong/Unexpected

**Issue**: USD prices too high or too low

**Solution**:
1. Check exchange rate is correct:
   ```sql
   SELECT rate FROM joomla_zencurrencyrates
   WHERE currency_code = 'USD'
   ORDER BY valid_from DESC LIMIT 1;
   ```
2. Should be around 1.25 - 1.30 (as of 2025)
3. If rate is 0.79, that's inverted! Update to 1.27

**Fix**:
```sql
-- Update the rate
UPDATE joomla_zencurrencyrates
SET rate = 1.27
WHERE currency_code = 'USD';

-- Recalculate all USD prices
UPDATE joomla_zenholidayprices p
JOIN joomla_zenholidayprices g ON (
    p.date_id = g.date_id AND
    p.type_id = g.type_id AND
    g.currency_code = 'GBP'
)
SET p.value = ROUND(g.value * 1.27, 0)
WHERE p.currency_code = 'USD';
```

### ForexPricing Plugin Not Running

**Check Plugin Events**:
```sql
SELECT * FROM joomla_extensions
WHERE element = 'forexpricing' AND type = 'plugin';
-- Check 'enabled' column = 1
-- Check 'folder' column = 'mrzen'
```

**Check Plugin File Exists**:
```bash
ls -la /path/to/joomla/plugins/mrzen/forexpricing/
# Should contain: forexpricing.php, forexpricing.xml
```

**Enable Plugin**:
- Go to: **Extensions** → **Plugins**
- Search: "forexpricing"
- Click to edit
- Status: **Enabled**
- **Save & Close**

---

## How the ForexPricing Plugin Works

### Automatic Trigger

The plugin hooks into Joomla's save event:

1. **You edit a holiday** in admin
2. **You save the holiday**
3. **Plugin detects** price changes
4. **For each GBP price**:
   - Get latest USD exchange rate from `joomla_zencurrencyrates`
   - Calculate: `USD value = GBP value × rate`
   - Round to nearest whole number
   - Check if USD price record exists
   - **If exists**: Update the value
   - **If not exists**: Create new price record
5. **All happens in ~1 second**

### Plugin Location

**File**: `/plugins/mrzen/forexpricing/forexpricing.php`

**Key Method**: `onContentAfterSave()`

**Database Tables**:
- Reads: `joomla_zencurrencyrates`
- Writes: `joomla_zenholidayprices`

---

## Testing Checklist

After generating USD prices:

- [ ] Database query shows USD prices exist
- [ ] USD price count = GBP price count (or close)
- [ ] Frontend currency switcher shows USD option
- [ ] Switching to USD updates all prices
- [ ] USD prices use $ symbol
- [ ] USD prices are reasonable (1.25-1.30× GBP prices)
- [ ] Book Now URLs contain `&currency=USD`
- [ ] Debug panel shows USD detection working

---

## Quick Reference

### Enable USD Currency
```sql
UPDATE joomla_zencurrencies SET state = 1 WHERE code = 'USD';
```

### Set Exchange Rate
```sql
INSERT INTO joomla_zencurrencyrates (currency_code, rate, valid_from, rounding_increment, state)
VALUES ('USD', 1.27, NOW(), 1, 1);
```

### Generate USD Prices (SQL)
```sql
SET @usd_rate = 1.27;
INSERT INTO joomla_zenholidayprices (date_id, type_id, currency_code, value, spaces, state, created, created_by)
SELECT date_id, type_id, 'USD', ROUND(value * @usd_rate, 0), spaces, state, NOW(), 0
FROM joomla_zenholidayprices
WHERE currency_code = 'GBP';
```

### Check USD Prices
```sql
SELECT currency_code, COUNT(*) FROM joomla_zenholidayprices GROUP BY currency_code;
```

---

## Need Help?

**Files to check**:
- Plugin: `/plugins/mrzen/forexpricing/forexpricing.php`
- Currency table: `joomla_zencurrencies`
- Rates table: `joomla_zencurrencyrates`
- Prices table: `joomla_zenholidayprices`

**Admin URLs**:
- Currencies: `/administrator/index.php?option=com_zenadmin&view=currencies`
- Currency Rates: `/administrator/index.php?option=com_zenadmin&view=currencyrates`
- Holidays: `/administrator/index.php?option=com_zenholidays&view=holidays`
- Plugins: `/administrator/index.php?option=com_plugins&filter[search]=forexpricing`

**Support**:
- Check error logs: `/administrator/logs/`
- Enable Joomla debug mode for detailed errors
- Test on staging first before production
