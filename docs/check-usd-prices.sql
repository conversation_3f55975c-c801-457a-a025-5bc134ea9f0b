-- Check if USD prices exist in database
-- Run this to see if you have any USD price records

-- 1. Check currency setup
SELECT * FROM joomla_zencurrencies WHERE code IN ('GBP', 'USD');

-- 2. Check exchange rates
SELECT * FROM joomla_zencurrencyrates WHERE currency_code = 'USD' ORDER BY valid_from DESC LIMIT 1;

-- 3. Check price counts by currency
SELECT
    currency_code,
    COUNT(*) as price_count,
    MIN(value) as min_price,
    MAX(value) as max_price,
    AVG(value) as avg_price
FROM joomla_zenholidayprices
WHERE state = 1
GROUP BY currency_code;

-- 4. Check if specific date has both currencies (example date_id = 1)
SELECT date_id, type_id, currency_code, value
FROM joomla_zenholidayprices
WHERE date_id = (SELECT MIN(id) FROM joomla_zenholidaydates LIMIT 1)
ORDER BY currency_code;

-- EXPECTED RESULTS:
-- Query 3 should show TWO rows:
-- GBP | 1234 | 500 | 5000 | 1800
-- USD | 1234 | 625 | 6250 | 2286   ← If this row is missing, you need to generate USD prices!
