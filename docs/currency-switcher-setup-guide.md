# Currency Switcher Setup Guide

## How to Add Currency Switcher to Footer

The currency switcher module (`mod_zencurrencyswitcher`) is already installed and enhanced. Here's how to add it to your footer:

### Option 1: Via Joomla Admin (Recommended)

1. **Log into <PERSON><PERSON><PERSON> Admin**
   - Go to: `https://yourdomain.com/administrator`

2. **Navigate to Module Manager**
   - Click: **Extensions** → **Modules**

3. **Find Currency Switcher Module**
   - Search for: "Currency Switcher" or "mod_zencurrencyswitcher"
   - Or click **New** to create a new instance

4. **Configure Module**
   - **Title**: "Currency Switcher" (or hide title if you want)
   - **Show Title**: No (recommended for footer)
   - **Position**: Select your footer position (e.g., `footer-1`, `footer-2`, `footer-right`)
     - Common footer positions in zenbase template:
       - `footer-1` (left footer)
       - `footer-2` (center footer)
       - `footer-3` (right footer)
   - **Status**: Published
   - **Access**: Public
   - **Menu Assignment**: On all pages (or select specific pages)

5. **Save & Close**

6. **View Frontend**
   - Go to your website and check the footer
   - You should see a currency dropdown with flag and symbol

---

### Option 2: Add to Template Footer Directly

If you want more control over placement, add it directly to the footer template:

**File**: `/templates/zenbase/html/footer.php` (or wherever your footer is)

**Add this code**:
```php
<?php
// Load currency switcher module
$currencyModule = JModuleHelper::getModule('mod_zencurrencyswitcher');
if ($currencyModule) {
    echo JModuleHelper::renderModule($currencyModule);
}
?>
```

**Or with custom styling**:
```php
<div class="footer-currency-switcher">
    <?php
    $currencyModule = JModuleHelper::getModule('mod_zencurrencyswitcher');
    if ($currencyModule) {
        $moduleParams = new JRegistry($currencyModule->params);
        // You can override params here if needed
        echo JModuleHelper::renderModule($currencyModule, ['style' => 'none']);
    }
    ?>
</div>
```

---

### Option 3: Load by Position

If you already have a module position in your footer, just add:

```php
<?php if ($doc->countModules('footer-currency')) : ?>
    <div class="footer-currency">
        <?php echo $doc->getBuffer('modules', 'footer-currency', ['style' => 'xhtml']); ?>
    </div>
<?php endif; ?>
```

Then publish the currency switcher module to position `footer-currency` via admin.

---

## Styling the Footer Currency Switcher

The module already has enhanced styling, but you may want to customize it for the footer:

**Add to your template CSS** (`/templates/zenbase/css/custom.css` or in template):

```css
/* Footer currency switcher */
.footer-currency-switcher .currency-switcher-enhanced {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-currency-switcher .currency-switcher-enhanced:hover {
    background: rgba(255, 255, 255, 0.15);
}

.footer-currency-switcher .currency-select {
    background: rgba(255, 255, 255, 0.9);
    color: #222;
}

/* If footer has dark background */
.footer-dark .currency-switcher-enhanced {
    color: #fff;
}
```

---

## Current Template Footer Positions

To check what positions are available in your zenbase template:

1. **Via Admin**:
   - Go to: **Extensions** → **Templates** → **Templates**
   - Click on: **zenbase Details and Files**
   - Look for `templateDetails.xml` to see available positions

2. **Via Frontend**:
   - Add `?tp=1` to any URL (e.g., `https://yourdomain.com/?tp=1`)
   - This shows all module positions on the page with red outlines
   - Look for footer positions

**Common zenbase footer positions** (check yours specifically):
- `footer-1` - Left footer column
- `footer-2` - Center footer column
- `footer-3` - Right footer column
- `footer` - Full-width footer

---

## Verifying It Works

After adding the currency switcher to footer:

1. **Visit any page** on your site
2. **Look for the currency dropdown** in the footer
3. **It should show**:
   - Country flag icon
   - Currency dropdown (with symbols: "$ USD", "£ GBP")
4. **Test switching**:
   - Select different currency
   - Page should reload
   - All prices should update
   - New currency should be selected in dropdown

---

## Troubleshooting

### Currency Switcher Not Showing

**Check**:
1. Module is **Published** (Status = Published)
2. Module position is correct (matches template positions)
3. Menu assignment is correct (shows on pages you're viewing)
4. Template is loading the position (check `index.php` for position rendering)

**Debug**:
```php
// Add this temporarily to your footer template
<?php
$modules = JModuleHelper::getModules('footer-1'); // your position
echo '<pre>Modules in footer-1: ' . count($modules) . '</pre>';
var_dump($modules);
?>
```

### Currency Switcher Shows But Doesn't Switch

**Check**:
1. JavaScript errors in browser console (F12)
2. Form is submitting correctly (network tab)
3. Session is working (cookies enabled)
4. GeoIP plugin is enabled: **Extensions** → **Plugins** → Search "geolocate"

### Styling Looks Wrong

**Check**:
1. CSS is loading (view page source, search for `currency-switcher-enhanced`)
2. CSS conflicts with footer styles
3. Z-index issues (make sure switcher is above other elements)

**Quick fix** - Add inline to module template:
```php
<style>
.currency-switcher-enhanced {
    z-index: 1000 !important;
    position: relative !important;
}
</style>
```

---

## Multiple Instances

You can have currency switcher in multiple places:
- **Header** (position: `header-right` or `navigation`)
- **Footer** (position: `footer-3`)
- **Sidebar** (position: `sidebar`)

Just create multiple module instances in admin, assign to different positions.

---

## Advanced: Custom Module Chrome

If you want special wrapper HTML around the currency switcher:

**File**: `/templates/zenbase/html/modules.php`

Add:
```php
function modChrome_currency($module, &$params, &$attribs)
{
    echo '<div class="currency-module-wrapper">';
    echo '<div class="currency-label">Currency:</div>';
    echo $module->content;
    echo '</div>';
}
```

Then in module settings:
- **Module Style**: Select "currency"

---

## Testing Checklist

After setup:
- [ ] Currency switcher visible in footer
- [ ] Dropdown shows "$ USD" and "£ GBP"
- [ ] Switching from GBP to USD works (page reloads, prices change)
- [ ] Switching from USD to GBP works
- [ ] Selection persists across page navigation
- [ ] Mobile view: switcher is accessible and usable
- [ ] Styling matches footer design

---

## Need Help?

**Check these files**:
- Module: `/modules/mod_zencurrencyswitcher/tmpl/default.php`
- Module XML: `/modules/mod_zencurrencyswitcher/mod_zencurrencyswitcher.xml`
- Template footer: `/templates/zenbase/footer.php` (or `index.php`)

**Joomla Admin URLs**:
- Module Manager: `/administrator/index.php?option=com_modules`
- Template Manager: `/administrator/index.php?option=com_templates`
- Plugin Manager: `/administrator/index.php?option=com_plugins`

**Quick Check**: Is the geolocate plugin enabled?
```sql
SELECT enabled FROM joomla_extensions
WHERE element = 'geolocate' AND type = 'plugin';
-- Should return: 1 (enabled)
```
