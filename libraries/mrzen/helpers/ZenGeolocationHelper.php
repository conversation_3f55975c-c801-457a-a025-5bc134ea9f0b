<?php
/**
 * � 2015
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, US.A
 *
 * @license GPLv2+ <https://www.gnu.org/licenses/gpl-2.0.html>
 * @copyright � 2015
 * <AUTHOR>
 *
 */

jimport('mrzen.helpers.ZenSessionHelper');
jimport('maxminddb.vendor.autoload');

use MaxMind\Db\Reader;

/**
 * Geolocation Helper class
 *
 * Unless otherwise noted,
 * the parameter `$ip` will default to the current visitor IP.
 *
 */
class ZenGeolocationHelper
{

    protected $reader;

    /**
     * Constructor
     */
    public function __construct()
    {
      $path = $this->getDbLocation();
      if ($path) {
        $this->reader = new Reader($path);
      }
      JModelLegacy::addIncludePath(JPATH_ADMINISTRATOR . '/components/com_zenadmin/models');
      $this->model = JModelLegacy::getInstance('Country', 'ZenAdminModel');

      // Initialize currency detection on construction
      $this->initializeCurrencyDetection();
    }

    /**
     * Initialize currency detection based on user's country
     * Sets USD for US visitors, GBP for all others
     */
    private function initializeCurrencyDetection()
    {
      $session = JFactory::getSession();

      // Only set currency if not already explicitly set
      if (!$session->get('__geoip_currency', false)) {
        $country = $this->getCountry();

        if ($country && isset($country->code)) {
          // US visitors get USD, all others get GBP
          $currency = ($country->code === 'US') ? 'USD' : 'GBP';
          $session->set('__geoip_currency', $currency);
        }
      }
    }

    /**
     * Get the user's country by IP
     *
     * @param string $ip IP Address
     *
     * @return \stdClass Country Information
     * @return null Country not identified.
     */
    public function getCountry($ip = null)
    {
        if ($domain = $this->getDomain()) {
          if ($domain->flag_code) {
            $countryCode =  $domain->flag_code;
            $country = $this->model->loadByCode($countryCode);
            // Skip cache against session so changing domain will change currency.
            return $country;
          }
        }

        // Only check session data if no IP supplied
        if (!$ip) {
            $session =& JFactory::getSession();
            $country = $session->get('_geoip_country', false);
        }

        // If country not found � Gettit!
        if (!$country) {
            $ipInfo = $this->lookup($ip);

            $countryCode = $ipInfo['country']['iso_code'];

            if (!$countryCode) {
                $countryCode = $this->getDefaultCountryCode();
            }

            $country = $this->model->loadByCode($countryCode);

            if ($ip) {
                $session->set('_geoip_country', $country);
            }
        }

        return $country;
    }


    /**
     * Look up an IP Address in the IP database
     *
     * @param string $ip IP Address
     */
    public function lookup($ip = null)
    {
      if (!$this->reader) {
        return false;
      }
        // Check for domain record
        if (!$ip) {
            $ip = ZenSessionHelper::getUsersIp();

            // Return null if no IP address is present.
            // Used for non web requests. (e.g. CLI)
            if (!$ip) {
                return null;
            }
        }

        return $this->reader->get($ip);
    }

    /**
     * Get the location of the MaxMind Database
     *
     * @return String MaxMind DB Location
     */
    public function getDbLocation()
    {
        // Check for a file path in the com_zenadmin component.
        $params = JComponentHelper::getParams('com_zenadmin');
        $path = $params->get('geoip_db_path', false);

        // Prepend JPATH_ROOT to relative paths.
        if ($path && stripos($path, '/') !== 0) {
            $path = JPATH_ROOT . '/' .  $path;
        }

        return $path;
    }

    /**
     * Get the default country
     *
     * @return \stdClass Default Country
     */
    public function getDefaultCountryCode()
    {
        $params = JComponentHelper::getParams('com_zenadmin');

        return $params->get('geoip_default_country', 'US');

    }


    public function getDomain()
    {
      $host = JUri::getInstance()->toString(['host']);

      $db = JFactory::getDbo();
      $q  = $db->getQuery(true);

      $q->select('*')
        ->from('`#__zendomains`')
        ->where('`host` = ' . $db->Quote($host));
      $db->setQuery($q);

      $db->setQuery($q);
      return $db->loadObject();
    }
}
