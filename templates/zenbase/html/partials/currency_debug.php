<?php
/**
 * Currency Debug Panel
 * Shows current currency detection state for testing
 *
 * To enable: Add to your template file:
 * <?php if (JFactory::getUser()->authorise('core.admin')) include(JPATH_THEMES . '/zenbase/html/partials/currency_debug.php'); ?>
 */

defined('_JEXEC') or die;

jimport('mrzen.helpers.ZenSessionHelper');
jimport('mrzen.helpers.ZenGeolocationHelper');

$session = JFactory::getSession();
$geo = new ZenGeolocationHelper();
$country = $geo->getCountry();
$userIp = ZenSessionHelper::getUsersIp();
$sessionCurrency = $session->get('__geoip_currency', 'Not Set');
$helperCurrency = ZenSessionHelper::getUsersCurrencyCode();
$userCurrencyObj = ZenSessionHelper::getUsersCurrency();

// Get cookie data
$cookieData = ZenSessionHelper::getLocaleCookie();

// URL parameter
$app = JFactory::getApplication();
$urlCurrency = $app->input->get('currency', 'Not Set');
?>

<style>
.currency-debug-panel {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #1a1a1a;
    color: #fff;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 9999;
    max-width: 350px;
    border: 2px solid #fe7720;
}
.currency-debug-panel h4 {
    margin: 0 0 10px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #fe7720;
    color: #fe7720;
    font-size: 14px;
    font-weight: bold;
}
.currency-debug-panel .debug-row {
    display: flex;
    justify-content: space-between;
    padding: 4px 0;
    border-bottom: 1px solid #333;
}
.currency-debug-panel .debug-row:last-child {
    border-bottom: none;
}
.currency-debug-panel .debug-label {
    font-weight: bold;
    color: #aaa;
}
.currency-debug-panel .debug-value {
    color: #4CAF50;
    font-weight: bold;
}
.currency-debug-panel .debug-value.highlight {
    color: #fe7720;
}
.currency-debug-panel .debug-controls {
    position: absolute;
    top: 8px;
    right: 10px;
    display: flex;
    gap: 10px;
}
.currency-debug-panel .debug-minimize,
.currency-debug-panel .debug-close {
    cursor: pointer;
    color: #aaa;
    font-weight: bold;
    font-size: 18px;
    background: none;
    border: none;
    padding: 0;
    line-height: 1;
}
.currency-debug-panel .debug-minimize:hover,
.currency-debug-panel .debug-close:hover {
    color: #fff;
}
.currency-debug-panel.minimized {
    height: auto;
    padding: 8px 15px;
}
.currency-debug-panel.minimized .debug-content {
    display: none;
}
.currency-debug-panel .debug-section {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #333;
}
</style>

<div class="currency-debug-panel" id="currencyDebugPanel">
    <div class="debug-controls">
        <button class="debug-minimize" id="debugMinimize" title="Minimize">_</button>
        <button class="debug-close" id="debugClose" title="Close">&times;</button>
    </div>
    <h4>💱 Currency Debug</h4>

    <div class="debug-content">
    <div class="debug-section">
        <strong style="color: #fe7720;">GeoIP Detection:</strong>
        <div class="debug-row">
            <span class="debug-label">Your IP:</span>
            <span class="debug-value"><?php echo $userIp ?: 'Unknown'; ?></span>
        </div>
        <div class="debug-row">
            <span class="debug-label">Detected Country:</span>
            <span class="debug-value highlight"><?php echo $country ? $country->code . ' - ' . $country->name : 'Unknown'; ?></span>
        </div>
    </div>

    <div class="debug-section">
        <strong style="color: #fe7720;">Currency State:</strong>
        <div class="debug-row">
            <span class="debug-label">URL Parameter:</span>
            <span class="debug-value"><?php echo $urlCurrency; ?></span>
        </div>
        <div class="debug-row">
            <span class="debug-label">Session Currency:</span>
            <span class="debug-value highlight"><?php echo $sessionCurrency; ?></span>
        </div>
        <div class="debug-row">
            <span class="debug-label">Helper Currency:</span>
            <span class="debug-value highlight"><?php echo $helperCurrency; ?></span>
        </div>
        <div class="debug-row">
            <span class="debug-label">Currency Symbol:</span>
            <span class="debug-value"><?php echo $userCurrencyObj ? $userCurrencyObj->symbol : 'N/A'; ?></span>
        </div>
    </div>

    <div class="debug-section">
        <strong style="color: #fe7720;">Cookie Data:</strong>
        <div class="debug-row">
            <span class="debug-label">Cookie Currency:</span>
            <span class="debug-value"><?php echo $cookieData ? $cookieData['currency'] : 'Not Set'; ?></span>
        </div>
        <div class="debug-row">
            <span class="debug-label">Cookie Language:</span>
            <span class="debug-value"><?php echo $cookieData ? $cookieData['language'] : 'Not Set'; ?></span>
        </div>
    </div>

    <div class="debug-section">
        <strong style="color: #fe7720;">Detection Logic:</strong>
        <div style="font-size: 11px; color: #aaa; margin-top: 8px; line-height: 1.4;">
            1. URL param<br>
            2. Session var<br>
            3. GeoIP lookup<br>
            4. Default (USD)
        </div>
    </div>

    <div class="debug-section" style="font-size: 10px; color: #666; margin-top: 10px;">
        <?php echo date('Y-m-d H:i:s'); ?> | Admin Only
    </div>
    </div><!-- /debug-content -->
</div>

<script>
// Debug panel functionality
(function() {
    const panel = document.getElementById('currencyDebugPanel');
    const closeBtn = document.getElementById('debugClose');
    const minimizeBtn = document.getElementById('debugMinimize');

    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;
    let xOffset = 0;
    let yOffset = 0;

    // Close button
    closeBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        panel.style.display = 'none';
    });

    // Minimize button
    minimizeBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        panel.classList.toggle('minimized');
        if (panel.classList.contains('minimized')) {
            minimizeBtn.textContent = '+';
            minimizeBtn.title = 'Expand';
        } else {
            minimizeBtn.textContent = '_';
            minimizeBtn.title = 'Minimize';
        }
    });

    // Make panel draggable
    panel.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', dragEnd);

    function dragStart(e) {
        // Don't drag if clicking on buttons
        if (e.target.id === 'debugClose' || e.target.id === 'debugMinimize') return;

        initialX = e.clientX - xOffset;
        initialY = e.clientY - yOffset;
        isDragging = true;
        panel.style.cursor = 'grabbing';
    }

    function drag(e) {
        if (isDragging) {
            e.preventDefault();
            currentX = e.clientX - initialX;
            currentY = e.clientY - initialY;
            xOffset = currentX;
            yOffset = currentY;
            setTranslate(currentX, currentY, panel);
        }
    }

    function dragEnd() {
        isDragging = false;
        panel.style.cursor = 'grab';
    }

    function setTranslate(xPos, yPos, el) {
        el.style.transform = `translate(${xPos}px, ${yPos}px)`;
    }

    // Set initial cursor
    panel.style.cursor = 'grab';
})();
</script>
